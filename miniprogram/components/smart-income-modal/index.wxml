<!-- 智能填写收入模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content smart-income-modal" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">智能填写收入</view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>

    <view class="modal-body">
      <!-- 计算模式选择 -->
      <view class="input-group">
        <view class="input-label">计算方式</view>
        <view class="calculation-modes">
          <view class="mode-option {{smartIncomeMode === 'total' ? 'active' : ''}}"
                bind:tap="onSelectIncomeMode"
                data-mode="total">
            <text class="mode-icon">💰</text>
            <view class="mode-content">
              <text class="mode-title">固定时薪</text>
              <text class="mode-desc">按时间比例分配总收入</text>
            </view>
          </view>

          <view class="mode-option {{smartIncomeMode === 'overtime' ? 'active' : ''}}"
                bind:tap="onSelectIncomeMode"
                data-mode="overtime">
            <text class="mode-icon">⏰</text>
            <view class="mode-content">
              <text class="mode-title">加班倍率</text>
              <text class="mode-desc">设置基础时薪和加班倍率</text>
            </view>
          </view>

          <view class="mode-option {{smartIncomeMode === 'hourly' ? 'active' : ''}}"
                bind:tap="onSelectIncomeMode"
                data-mode="hourly">
            <text class="mode-icon">🕐</text>
            <view class="mode-content">
              <text class="mode-title">分类时薪</text>
              <text class="mode-desc">为不同类型设置时薪</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 总收入分配模式 -->
      <view class="calculation-content" wx:if="{{smartIncomeMode === 'total'}}">
        <view class="input-group">
          <view class="input-with-calculator">
            <view class="input-label input-label-with-calculator">当日总收入</view>
            <view class="daily-income-calculator-btn" bind:tap="onOpenDailyIncomeCalculator">
              <text class="calculator-icon">✨</text>
              <text class="calculator-text">计算日收入</text>
            </view>
          </view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入"
                   value="{{smartIncomeTotalAmountText || smartIncomeTotalAmount}}"
                   bind:input="onTotalAmountChange"/>
            <text class="input-unit">元</text>
          </view>
        </view>
      </view>

      <!-- 加班倍率模式 -->
      <view class="calculation-content" wx:if="{{smartIncomeMode === 'overtime'}}">
        <!-- 计算方式选择 -->
        <view class="calculation-method-selector">
          <view class="method-option {{smartIncomeOvertimeCalculationMethod === 'hourly' ? 'active' : ''}}"
                bind:tap="onSelectOvertimeCalculationMethod"
                data-method="hourly">
            <text class="method-icon">💰</text>
            <text class="method-title">基础时薪</text>
            <text class="method-desc">输入基础时薪和倍率</text>
          </view>

          <view class="method-option {{smartIncomeOvertimeCalculationMethod === 'total' ? 'active' : ''}}"
                bind:tap="onSelectOvertimeCalculationMethod"
                data-method="total">
            <text class="method-icon">📊</text>
            <text class="method-title">总收入</text>
            <text class="method-desc">输入总收入和倍率</text>
          </view>
        </view>

        <!-- 基础时薪方式 -->
        <view wx:if="{{smartIncomeOvertimeCalculationMethod === 'miniprogram/core/managers/sync-manager.js:110hourly'}}">
          <view class="input-group">
            <view class="input-label">基础时薪</view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入"
                     value="{{smartIncomeBaseHourlyText || smartIncomeBaseHourly}}"
                     bind:input="onBaseHourlyChange" />
              <text class="input-unit">元/小时</text>
            </view>
          </view>
        </view>

        <!-- 总收入方式 -->
        <view wx:if="{{smartIncomeOvertimeCalculationMethod === 'total'}}">
          <view class="input-group">
            <view class="input-with-calculator">
              <view class="input-label input-label-with-calculator">当日总收入</view>
              <view class="daily-income-calculator-btn" bind:tap="onOpenDailyIncomeCalculatorForOvertime">
                <text class="calculator-icon">✨</text>
                <text class="calculator-text">计算日收入</text>
              </view>
            </view>
            <view class="input-with-unit">
              <input class="number-input"
                     type="digit"
                     placeholder="请输入"
                     value="{{smartIncomeOvertimeTotalAmountText || smartIncomeOvertimeTotalAmount}}"
                     bind:input="onOvertimeTotalAmountChange"/>
              <text class="input-unit">元</text>
            </view>
          </view>
        </view>

        <!-- 加班倍率 -->
        <view class="input-group">
          <view class="input-label">加班倍率</view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入"
                   value="{{smartIncomeOvertimeRateText || smartIncomeOvertimeRate}}"
                   bind:input="onOvertimeRateChange" />
            <text class="input-unit">倍</text>
          </view>
        </view>
      </view>

      <!-- 分类时薪模式 -->
      <view class="calculation-content input-groups" wx:if="{{smartIncomeMode === 'hourly'}}">
        <view class="input-group">
          <view class="input-label">工作时薪</view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入"
                   value="{{smartIncomeWorkHourlyText || smartIncomeWorkHourly}}"
                   bind:input="onWorkHourlyChange" />
            <text class="input-unit">元/小时</text>
          </view>
        </view>

        <view class="input-group">
          <view class="input-label">加班时薪</view>
          <view class="input-with-unit">
            <input class="number-input"
                   type="digit"
                   placeholder="请输入"
                   value="{{smartIncomeOvertimeHourlyText || smartIncomeOvertimeHourly}}"
                   bind:input="onOvertimeHourlyChange" />
            <text class="input-unit">元/小时</text>
          </view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <view class="btn-secondary modal-btn" bind:tap="onCancel">
        <text>取消</text>
      </view>
      <view class="btn-primary modal-btn" bind:tap="onConfirm">
        <text>应用计算</text>
      </view>
    </view>
  </view>

  <!-- 日收入计算器 -->
  <daily-income-calculator
    visible="{{showDailyIncomeCalculator}}"
    monthly-income="{{dailyIncomeCalculatorMonthlyIncome}}"
    target-mode="{{dailyIncomeCalculatorTargetMode}}"
    bind:confirm="onDailyIncomeCalculatorConfirm"
    bind:cancel="onDailyIncomeCalculatorCancel">
  </daily-income-calculator>
</view>
