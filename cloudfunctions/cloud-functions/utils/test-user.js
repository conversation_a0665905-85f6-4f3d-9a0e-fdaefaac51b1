/**
 * 测试用户相关工具函数
 *
 * 注意：测试用户的管理说明
 * 1. 用户注册时，isTestUser 字段会根据此文件中的配置自动设置
 * 2. 用户注册后，测试用户状态只能通过数据库手动修改，不能通过代码动态修改
 * 3. 云函数是无状态的，每次调用都会重新初始化，所以内存中的变量修改不会持久化
 * 4. 如需修改用户的测试状态，请直接在数据库中更新用户记录的 isTestUser 字段
 */

/**
 * 测试用户openid列表
 * 在这里配置测试用户的openid，仅用于新用户注册时的初始判断
 *
 * 重要：此列表仅在用户注册时生效，注册后的测试用户状态修改需要直接操作数据库
 */
const TEST_USER_OPENIDS = [
  // 在这里添加测试用户的openid
  // 'test_openid_1',
  // 'test_openid_2'
]

/**
 * 检查用户是否为测试用户
 * 此方法仅在用户注册时调用，用于设置初始的测试用户状态
 *
 * @param {string} openid - 用户openid
 * @returns {boolean} 是否为测试用户
 */
function isTestUser(openid) {
  if (!openid) {
    return false
  }

  return TEST_USER_OPENIDS.includes(openid)
}

module.exports = {
  isTestUser
}
